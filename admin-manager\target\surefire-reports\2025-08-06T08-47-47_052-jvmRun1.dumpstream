# Created at 2025-08-06T08:47:48.631
TestSet has not finished before stream error has appeared >> initializing exit by non-null configuration: null
java.io.EOFException
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at org.apache.maven.surefire.booter.MasterProcessCommand.decode(MasterProcessCommand.java:115)
	at org.apache.maven.surefire.booter.CommandReader$CommandRunnable.run(CommandReader.java:391)
	at java.lang.Thread.run(Thread.java:750)


# Created at 2025-08-06T08:47:48.666
Unexpected IOException: 1,1,org.apache.maven.surefire.junitplatform.JUnitPlatformProvider,com.gl.service.user.service.WechatUserServiceTest,null,null,null


# Created at 2025-08-06T08:47:48.692
Unexpected IOException: 5,1,com.gl.service.user.service.WechatUserServiceTest,testList_Success_WithData,null,null,null


# Created at 2025-08-06T08:47:50.755
Unexpected IOException with stream: SLF4J: Class path contains multiple SLF4J bindings.


# Created at 2025-08-06T08:47:50.755
Unexpected IOException with stream: SLF4J: Found binding in [jar:file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar!/org/slf4j/impl/StaticLoggerBinder.class]


# Created at 2025-08-06T08:47:50.757
Unexpected IOException with stream: SLF4J: Found binding in [jar:file:/C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.31/slf4j-simple-1.7.31.jar!/org/slf4j/impl/StaticLoggerBinder.class]


# Created at 2025-08-06T08:47:50.757
Unexpected IOException with stream: SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.


# Created at 2025-08-06T08:47:50.810
Unexpected IOException with stream: SLF4J: Actual binding is of type [ch.qos.logback.classic.util.ContextSelectorStaticBinder]


# Created at 2025-08-06T08:47:51.231
Unexpected IOException: 6,1,com.gl.service.user.service.WechatUserServiceTest,testList_Success_WithData,null,null,null


# Created at 2025-08-06T08:47:51.237
Unexpected IOException: 2,1,org.apache.maven.surefire.junitplatform.JUnitPlatformProvider,com.gl.service.user.service.WechatUserServiceTest,null,null,null


