package com.gl.service.user.service;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.user.vo.SysAreaTreeVo;
import com.gl.service.user.vo.WechatUserVo;
import com.gl.service.user.vo.dto.WechatUserDto;
import com.gl.system.entity.SysArea;
import com.gl.system.repository.SysAreaRepository;
import com.gl.system.vo.SysUserVo;
import com.gl.wechat.entity.WechatUser;
import com.gl.wechat.repository.WechatUserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WechatUserService单元测试类
 * 测试微信用户服务的所有公共方法，包括正面和负面场景
 * 
 * @author: Test
 * @date: 2024/12/19
 * @version: 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("微信用户服务单元测试")
class WechatUserServiceTest {

    @Mock(lenient = true)
    private JdbcTemplate jdbcTemplate;

    @Mock(lenient = true)
    private SysAreaRepository sysAreaRepository;

    @Mock(lenient = true)
    private WechatUserRepository wechatUserRepository;

    @InjectMocks
    private WechatUserService wechatUserService;

    private WechatUserDto mockDto;
    private WechatUserVo mockWechatUserVo;
    private WechatUser mockWechatUser;
    private SysArea mockSysArea;
    private LoginUser mockLoginUser;
    private SysUserVo mockSysUserVo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockDto = new WechatUserDto();
        mockDto.setGender(1);
        mockDto.setAddress("110000");
        mockDto.setSearchCondition("测试用户");
        mockDto.setBeginTime("2023-01-01");
        mockDto.setEndTime("2023-12-31");
        mockDto.setPageNumber(0);
        mockDto.setPageSize(10);

        mockWechatUserVo = new WechatUserVo();
        mockWechatUserVo.setId(1L);
        mockWechatUserVo.setOpenid("test_openid");
        mockWechatUserVo.setNickname("测试用户");
        mockWechatUserVo.setPhone("13800138000");

        mockWechatUser = new WechatUser();
        mockWechatUser.setId(1L);
        mockWechatUser.setPhone("13800138000");

        mockSysArea = new SysArea();
        mockSysArea.setId(110000L);
        mockSysArea.setName("北京市");
        mockSysArea.setParentId(0L);

        mockSysUserVo = new SysUserVo();
        mockSysUserVo.setId(1L);
        mockSysUserVo.setSiteId(1L);

        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockSysUserVo);
    }

    @Test
    @DisplayName("查询微信用户列表 - 成功场景，有数据返回")
    void testList_Success_WithData() {
        // Given
        when(sysAreaRepository.findById(anyLong())).thenReturn(Optional.of(mockSysArea));
        // Mock count query - 注意这里需要匹配实际的参数数量
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);
        // Mock data query
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(Arrays.asList(mockWechatUserVo));

        // When
        Result result = wechatUserService.list(mockDto, 1);

        // Then
        assertNotNull(result, "结果不应为空");
        assertEquals(10000, result.getCode(), "状态码应为10000");
        assertNotNull(result.getData(), "数据不应为空");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(1L, data.get("total"), "总数应为1");
        assertNotNull(data.get("result"), "结果列表不应为空");
    }

    @Test
    @DisplayName("查询微信用户列表 - 成功场景，无数据返回")
    void testList_Success_NoData() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(0L);

        // When
        Result result = wechatUserService.list(mockDto, 1);

        // Then
        assertNotNull(result, "结果不应为空");
        assertEquals(10000, result.getCode(), "状态码应为10000");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, data.get("total"), "总数应为0");
        assertNull(data.get("result"), "结果列表应为空");
    }

    @Test
    @DisplayName("查询微信用户列表 - 成功场景，空DTO参数")
    void testList_Success_NullDto() {
        // Given - 当dto为null时，没有额外的查询条件，args为空数组
        // 使用exportType=0避免分页逻辑中的dto!=null断言
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(Arrays.asList(mockWechatUserVo));

        // When
        Result result = wechatUserService.list(null, 0);

        // Then
        assertNotNull(result, "结果不应为空");
        assertEquals(10000, result.getCode(), "状态码应为10000");

        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(1L, data.get("total"), "总数应为1");
        assertNotNull(data.get("result"), "结果列表不应为空");
    }

    @Test
    @DisplayName("查询微信用户列表 - 异常场景，数据库查询失败")
    void testList_Exception_DatabaseError() {
        // Given - 重置所有mock以避免之前测试的干扰
        reset(jdbcTemplate, sysAreaRepository);

        // 首先需要模拟sysAreaRepository.findById()成功，因为它在jdbcTemplate.queryForObject()之前被调用
        when(sysAreaRepository.findById(anyLong())).thenReturn(Optional.of(mockSysArea));

        // 然后模拟jdbcTemplate.queryForObject()抛出异常
        // 使用doThrow来确保异常被抛出，注意这里使用varargs而不是Object[]
        doThrow(new RuntimeException("数据库连接失败"))
                .when(jdbcTemplate)
                .queryForObject(anyString(), eq(Long.class), any(), any(), any(), any(), any(), any());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> wechatUserService.list(mockDto, 1),
                "应抛出数据库连接异常");

        // 验证异常消息
        assertEquals("数据库连接失败", exception.getMessage(), "异常消息应该正确");

        // 验证mock被调用
        verify(jdbcTemplate).queryForObject(anyString(), eq(Long.class), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("导出微信用户列表 - 成功场景")
    void testExportList_Success() throws IOException {
        // Given
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        when(mockResponse.getOutputStream()).thenReturn(mock(javax.servlet.ServletOutputStream.class));

        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(1L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any(Object[].class)))
                .thenReturn(Arrays.asList(mockWechatUserVo));

        // When
        assertDoesNotThrow(() -> wechatUserService.exportList(mockDto, mockResponse));

        // Then
        verify(mockResponse).setContentType("application/vnd.ms-excel");
        verify(mockResponse).setCharacterEncoding("utf-8");
    }

    @Test
    @DisplayName("获取区域列表 - 成功场景，无搜索条件")
    void testAreaList_Success_NoSearchCondition() {
        // Given
        List<SysArea> mockAreas = Arrays.asList(mockSysArea);
        when(sysAreaRepository.findAll()).thenReturn(mockAreas);

        // When
        List<SysAreaTreeVo> result = wechatUserService.areaList(null);

        // Then
        assertNotNull(result, "结果不应为空");
        assertEquals(1, result.size(), "结果数量应为1");
        assertEquals("北京市", result.get(0).getName(), "区域名称应正确");
    }

    @Test
    @DisplayName("获取区域列表 - 成功场景，有搜索条件")
    void testAreaList_Success_WithSearchCondition() {
        // Given
        String searchName = "北京";
        List<SysArea> mockAreas = Arrays.asList(mockSysArea);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(searchName)))
                .thenReturn(mockAreas);

        // When
        List<SysAreaTreeVo> result = wechatUserService.areaList(searchName);

        // Then
        assertNotNull(result, "结果不应为空");
        assertEquals(1, result.size(), "结果数量应为1");
        assertEquals("北京市", result.get(0).getName(), "区域名称应正确");
    }

    @Test
    @DisplayName("获取区域列表 - 异常场景，数据库查询失败")
    void testAreaList_Exception_DatabaseError() {
        // Given
        when(sysAreaRepository.findAll()).thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then
        assertThrows(RuntimeException.class, () -> wechatUserService.areaList(null),
                "应抛出数据库连接异常");
    }

    @Test
    @DisplayName("检查手机号绑定状态 - 成功场景，用户已绑定手机号")
    void testCheckPhone_Success_PhoneBound() {
        // Given
        mockWechatUser.setPhone("13800138000");

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(wechatUserRepository.existsById(1L)).thenReturn(true);
            when(wechatUserRepository.getById(1L)).thenReturn(mockWechatUser);

            // When
            Result result = wechatUserService.checkPhone();

            // Then
            assertNotNull(result, "结果不应为空");
            assertEquals(10000, result.getCode(), "状态码应为10000");
            assertEquals(false, result.getData(), "手机号已绑定，应返回false");
        }
    }

    @Test
    @DisplayName("检查手机号绑定状态 - 成功场景，用户未绑定手机号")
    void testCheckPhone_Success_PhoneNotBound() {
        // Given
        mockWechatUser.setPhone(null);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(wechatUserRepository.existsById(1L)).thenReturn(true);
            when(wechatUserRepository.getById(1L)).thenReturn(mockWechatUser);

            // When
            Result result = wechatUserService.checkPhone();

            // Then
            assertNotNull(result, "结果不应为空");
            assertEquals(10000, result.getCode(), "状态码应为10000");
            assertEquals(true, result.getData(), "手机号未绑定，应返回true");
        }
    }

    @Test
    @DisplayName("检查手机号绑定状态 - 边界场景，用户不存在")
    void testCheckPhone_Edge_UserNotExists() {
        // Given
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);
            when(wechatUserRepository.existsById(1L)).thenReturn(false);

            // When
            Result result = wechatUserService.checkPhone();

            // Then
            assertNotNull(result, "结果不应为空");
            assertEquals(10000, result.getCode(), "状态码应为10000");
            assertEquals(false, result.getData(), "用户不存在，应返回false");
        }
    }

    @Test
    @DisplayName("检查手机号绑定状态 - 异常场景，SecurityUtils获取用户失败")
    void testCheckPhone_Exception_SecurityUtilsError() {
        // Given
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser)
                    .thenThrow(new RuntimeException("获取用户信息失败"));

            // When & Then
            assertThrows(RuntimeException.class, () -> wechatUserService.checkPhone(),
                    "应抛出获取用户信息异常");
        }
    }
}
